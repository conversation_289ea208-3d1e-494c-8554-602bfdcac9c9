#include "protheus.ch"
#include "msmgadd.ch"

#define cIniFile "SIGACLI.INI"
#define ENTER CHR(13) + CHR(10)

Static __lSrv
Static __aSrv
Static aSITCLI
Static __aVersion

User Function CLIniFile()
Return cIniFile

User Function Emergenc()
Local nQtdDias		:= SuperGetmv("TI_QTDDEME",,15) - 1 //Quantidade máxima de dias para gerar emergencial
Local oDlg
Local lGet			:= .T.
Local oLabel, oCode
Local _lCanGo		:= .T.
Local dDataAte		:= dDatabase

U_CliServ()

DEFINE MSDIALOG oDlg TITLE "Autorização de Emergência" FROM 00,00 TO 220,298 PIXEL

@02,05 SAY "Data de Referência:" PIXEL
oCal:=MsCalend():New(11,05,oDlg)
oCal:bChange := {|| oCode:SetText(CalcEmer(oCal:dDiaAtu)), _lCanGo := VldDataEme(dDataBase,oCal:dDiaAtu,nQtdDias), dDataAte := oCal:dDiaAtu/*Alert("Dia Selecionado " + oCal:dDiaAtu)*/}

@80,05 GROUP oGrp TO 95,143 PIXEL
@86,07 SAY oLabel VAR "Código:" PIXEL SIZE 36,8
@86,60 SAY oCode VAR CalcEmer(dDatabase) PIXEL SIZE 50,8 COLOR RGB(255,0,0),oDlg:nClrPane

@98,78 BUTTON "&Enviar" PIXEL SIZE 30,11 ACTION Iif(_lCanGo,U_SendEmerg(dDataAte),MsgAlert("Divergencia entre as datas, por favor verificar","Divergência entre datas")) //Caso data estiver divergente apresenta mensagem se não segue processo.

@98,113 BUTTON "&Fechar" PIXEL SIZE 30,11 ACTION oDlg:End()

ACTIVATE MSDIALOG oDlg CENTERED

Return NIL

User Function SendEmerg(dDataAte)
Local dData2 //Alimentada pela variável dDataAte
Local dData1 		:= dDataBase
Local nQtdDias		:= SuperGetmv("TI_QTDDEME",,15) - 1
Local cMail 		:= Space(600)
Local lRet 		:= .F.
Local aBtn 		:= Array(2)
Default dDataAte 	:= dDatabase

dData2 	:= dDataAte

DEFINE MSDIALOG oDlg TITLE "Enviar Autorização" FROM 00,00 TO 130,200 PIXEL

@06,05 SAY "De:" PIXEL
@05,20 GET dData1 PIXEL  WHEN .F.

@21,05 SAY "Até:" PIXEL
@20,20 GET dData2 PIXEL VALID (VldDataEme(dData1,dData2,nQtdDias))

@36,05 SAY "Para:" PIXEL
@35,20 MSGET cMail SIZE 78,10 PICTURE "@" PIXEL F3 "_EM" VALID RelValEmail(cMail)

DEFINE SBUTTON aBtn[1] TYPE 1 FROM 50,40 ENABLE ACTION (lRet := .T., oDlg:End())

DEFINE SBUTTON aBtn[2] TYPE 2 FROM 50,70 ACTION oDlg:End() ENABLE

ACTIVATE MSDIALOG oDlg CENTERED

If lRet
	MsgRun(Padc("Enviando...",100),,{|| SendNow(dData1,dData2,cMail)})
EndIf

Return lRet

Static Function VldDataEme(dData1,dData2,nQtdDias)
Local _lRet := .F.

If ((dData1 > dData2) .OR. (date() <  dData1))
	MsgAlert("Divergencia entre as datas informadas, por favor verificar.","Divergência entre datas")
ElseIf DateDiffDay(dData1, dData2) > nQtdDias
	MsgAlert("Período entre as datas não pode ultrapassar quantidade de " + CVALTOCHAR(SuperGetmv("TI_QTDDEME",,15)) + " dia(s)","Divergência entre datas")
Else
	_lRet := .T.
EndIf

Return _lRet

Static Function SendNow(dData1,dData2,cMail)
Local cText := ""
Local dData

SET CENTURY ON
dData := dData1
While dData >= dData1 .And. dData <= dData2
	cText += Dtoc(dData)+" - "+CalcEmer(dData)+"<br>"
	dData += 1
End
SET CENTURY OFF

IF (GETMV('TI_NVEMAIL',,.T.))
   U_xSendMail( cMail,"Autorização de Emergência... ",cText  ,,.T., ,,.T.,.f.)
Else
   SetMail(cMail,"Autorização de Emergência",cText,,.T.,.T.)
EndIf   

Return

User Function MDIOK()
Local lRet := .F.

If ShowSenhap()
	lRet := .T.
	U_CLSenhap(oApp:Cargo)
EndIf
Return lRet

User Function Esp1Load()
U_CliUpdLog()
Return

Static Function ShowSenhap()
Local ni
Local cSenhap

If GetSrvProfString("CLIDEBUG","00") == "0"
	cSenhap := GetSenhaP(Space(20),Space(8),"MSGPSW")
	
	If cSenhap < "008" .And. !(cSenhap $ "00156590,00158917")
		Return .F.
	EndIf
	
	For ni := 1 To 8
		If !(Subs(cSenhap,ni,1)$"0123456789ABCDEF")
			Return .F.
		Endif
	Next
Else
	cSenhap := "CLIDEBUG"
EndIf
U_CLSenhap(cSenhap)
Return .T.

User Function SIGAESP1()

U_CliServ()
If Empty(U_CLSenhap())
	If SetMDIChild(0)
		U_CLSenhap(oApp:Cargo)
	ElseIf !ShowSenhap()
		Final("Usuário ou senha inválido")
	Else
		SetOnExit("U_CLFinal")
	EndIf
	
	#IFDEF TOP
	
/*		If Select("SE1JMC") == 0
		    
			EmpOPenFile( "SE1JMC", "SE1", 2, .T., "73")
			*/
			/*If !MsOpenDbf( .T. ,__cRDD, "SE1730", "SE1JMC",.T.,.F.)
				Final("Arquivo não encontrado","SE1730")
			EndIf*/
//		EndIf
		
		If Select("SE1RIO") == 0

			EmpOPenFile( "SE1RIO", "SE1", 2, .T., "01") // Antiga 75
			
			/*If !MsOpenDbf( .T. ,__cRDD, "SE1750", "SE1RIO",.T.,.F.)
				Final("Arquivo não encontrado","SE1750")
			EndIf*/
		EndIf
		
	#ELSE
	
		If Select("SE1JMC") == 0
			If !MsOpenDbf( .T. ,__cRDD, "\DADOS\EMP73\SE1730.DBF", "SE1JMC",.T.,.F.)
				Final("Arquivo não encontrado","\DADOS\EMP73\SE1730.DBF")
			EndIf
	
			DbSelectArea("SE1JMC")
			If ( File("SE1JMC.CDX") )
				DbSetIndex("SE1JMC")
			Else
				INDEX ON &("E1_FILIAL+E1_CLIENTE") TAG &("SE1JMC1") TO &("SE1JMC")
			EndIf
		EndIf
		
		If Select("SE1RIO") == 0
			If !MsOpenDbf( .T. ,__cRDD, "\DADOS\EMP75\SE1750.DBF", "SE1RIO",.T.,.F.)
				Final("Arquivo não encontrado","\DADOS\EMP75\SE1750.DBF")
			EndIf
				
			DbSelectArea("SE1RIO")
			If ( File("SE1RIO.CDX") )
				DbSetIndex("SE1RIO")
			Else
				INDEX ON &("E1_FILIAL+E1_CLIENTE") TAG &("SE1RIO1") TO &("SE1RIO")
			EndIf
		EndIf
		
	#ENDIF
EndIf
Return NIL

User Function CLFinal()
U_CLSenhap("")
Return

User Function ValPass(cSenhaC, cCSenha, lSenha, oDlg, nTime, lAbort)
nTime++

If ( nTime <= 3 )
	If ( Upper(cSenhaC) == Upper(cCSenha) )
		lSenha := .T.
		lAbort := .F.
		oDlg:End()
	Else
        MSGSTOP("Senha Inválida","")
		lSenha := .F.
		lAbort := .T.
	EndIf
Else
	lSenha := .F.
	lAbort := .F.
	oDlg:End()
EndIf

Return NIL

User Function CliConfig( )

DEFINE SBUTTON oBtn FROM 66,24 TYPE 13 ACTION U_CliCopyData() OF oDlg ENABLE ONSTOP "Copiar Base"

Return NIL

User Function CliUpdLog( )
Local cFileUpd := 'LOGSENHA.DBF'
Local cSvAlias := Alias()

Private cAliasUpd := 'LOGSENHA'

u_LogGen("Função CliUpdLog","Verificar pois entrou na função CliUpdLog.")

If ( File(cFileUpd) )

	//Função não utilizada em nenhum lugar alem da função Esp1Load, colocarei para avisar se for utilizado
	//DbUseArea(.T.,__LocalDriver,cFileUpd,cAliasUpd,.T.)	

	If ( !NetErr() )
		Processa({|| U_CliReadLog()},'Aguarde','Atualizando liberacoes geradas pela Internet ...',.F.)
		DbSelectArea(cAliasUpd)
		DbCloseArea()
		Ferase(cFileUpd)
	EndIf
EndIf
DbSelectArea(cSvAlias)
Return

Static ;
User Function CliReadLog( )

DbSelectArea(cAliasUpd)
DbGoTop()

ProcRegua(Reccount())

While ( !Eof() )
	IncProc()
	If !( (cAliasUpd)->ZA3_CHECKE )
		DbSelectArea('ZA1')
		DbSetOrder(2)
		If ( DbSeek(xFilial()+(cAliasUpd)->ZA3_CODIGO+(cAliasUpd)->ZA3_CODFIL) )
			While ( !Eof() .and. ZA1->ZA1_CODIGO == (cAliasUpd)->ZA3_CODIGO .and. ZA1->ZA1_CODFIL == (cAliasUpd)->ZA3_CODFIL )
				If ( ZA1->ZA1_CLIENT == (cAliasUpd)->ZA3_CLIENT .and. ;
					 ZA1->ZA1_NOMEEM == (cAliasUpd)->ZA3_NOMEEM .and. ;
					 ZA1->ZA1_NOMEFI == (cAliasUpd)->ZA3_NOMEFI .and. ;
					 ZA1->ZA1_VERSAO == (cAliasUpd)->ZA3_VERSAO)
					RecLock('ZA1',.F.)
					ZA1->ZA1_DTREF := (cAliasUpd)->ZA3_DATA
					MsUnlock()
					DbSelectArea('ZA3')
					RecLock('ZA3',.T.)
					ZA3->ZA3_FILIAL := xFilial('ZA3')
					ZA3->ZA3_CLIENT := (cAliasUpd)->ZA3_CLIENT
					ZA3->ZA3_CODIGO := (cAliasUpd)->ZA3_CODIGO
					ZA3->ZA3_CODFIL := (cAliasUpd)->ZA3_CODFIL
					ZA3->ZA3_NOMEEM := (cAliasUpd)->ZA3_NOMEEM
					ZA3->ZA3_NOMEFI := (cAliasUpd)->ZA3_NOMEFI
					ZA3->ZA3_VERSAO := (cAliasUpd)->ZA3_VERSAO
					ZA3->ZA3_USUARI := (cAliasUpd)->ZA3_USUARI
					ZA3->ZA3_DATA	:= (cAliasUpd)->ZA3_DATA
					ZA3->ZA3_MODULO := (cAliasUpd)->ZA3_MODULO
					ZA3->ZA3_VENC 	:= (cAliasUpd)->ZA3_VENC
					ZA3->ZA3_LIBESP := (cAliasUpd)->ZA3_LIBESP
					MsUnlock()
					Exit
				EndIf
				DbSelectArea('ZA1')
				DbSkip()
			End
		EndIf
	EndIf
    DbSelectArea(cAliasUpd)
	DbSkip()
End
DbCommitAll()
Return

User Function CliServ()
Local oRpcSrv

If __lSrv == NIL
	__lSrv := (GetSrvProfString("sigacli","1") == "1")
	__aSrv := Array(3)
	__aSrv[1] := Upper(GetSrvProfString("clienv",""))
	__aSrv[2] := Upper(GetSrvProfString("cliserv",""))
	__aSrv[3] := Val(Upper(GetSrvProfString("cliport","")))
EndIf

If !__lSrv
	oRpcSrv	:= TRpc():New(__aSrv[1])
	If oRpcSrv:Connect(__aSrv[2],__aSrv[3])
		Final("O Servidor "+__aSrv[2]+" ja esta no ar.")
	EndIf
EndIf
Return __lSrv

User Function CliPassword()
Local lRet := .F.
Local cSenhaC := Space(255)

DEFINE MSDIALOG oDlg TITLE "Digite a senha" FROM 0,0 TO 5,25 STYLE DS_MODALFRAME 

@05,05 MSGET cSenhaC PIXEL PASSWORD SIZE 90,10

DEFINE SBUTTON oOK TYPE 1 FROM 23,40 ENABLE;
ACTION If(Upper(AllTrim(cSenhaC)) == Upper(AllTrim(GetEnvServer())),(lRet := .T.,oDlg:End()),)

DEFINE SBUTTON oCancel TYPE 2 FROM 23,70 ACTION oDlg:End() ENABLE

ACTIVATE MSDIALOG oDlg CENTERED
Return lRet

User Function CLViewCli(cCodigo)
Local oTmp
Local cSvAlias := Alias()
Local aFields := {'A1_COD','A1_NOME','A1_NREDUZ','A1_TEL','A1_FAX','A1_EMAIL','A1_XSITCLI'}
Local aInfo
Local nX := 1
Local oTISX := TIGetSXS():New()

oTISX:SetSX3Fields(aFields)

DbSelectArea("SA1")
DbSetOrder(1)
If DbSeek(xFilial(" SA1")+cCodigo)	

	For nX:= 1 to len(aFields)

		ADD FIELD aInfo ;
		TITULO 	oTISX:GetSX3FldValue(aFields[nX],'X3_TITULO'	) ;
		CAMPO 	oTISX:GetSX3FldValue(aFields[nX],'X3_CAMPO'		) ;
		TIPO 	oTISX:GetSX3FldValue(aFields[nX],'X3_TIPO'		);
		TAMANHO oTISX:GetSX3FldValue(aFields[nX],'X3_TAMANHO'	) ;
		DECIMAL oTISX:GetSX3FldValue(aFields[nX],'X3_DECIMAL'	);
		PICTURE oTISX:GetSX3FldValue(aFields[nX],'X3_PICTURE'	);
		NIVEL 	1 ;
		BOX oTISX:GetSX3FldValue('X3_CBOX')

		M->&(oTISX:GetSX3FldValue(aFields[nX],'X3_CAMPO')) := SA1->&(oTISX:GetSX3FldValue(aFields[nX],'X3_CAMPO'))

	Next	

	DEFINE MSDIALOG oTmp TITLE "Cadastro de Clientes" FROM 9,0 TO 38,90

	Enchoice("SA1",Recno(),2,,,,,{26,02,__DlgHeight(oTmp),__DlgWidth(oTmp)},,,,,,oTmp,,,.T.,,.T.,.T.,aInfo)

	ACTIVATE MSDIALOG oTmp ON INIT EnchoiceBar(oTmp,{|| oTmp:End()},{|| oTmp:End()}) CENTERED
EndIf 

If Empty(cSvAlias)
	DbSelectArea(cSvAlias)
EndIf
Return

User Function CLLibMan(cCliente,lMsg)
Local cSvAlias := Alias()
Local lRet := .F.

DEFAULT lMsg := .T.

DbSelectArea("SCB")
DbSetOrder(1)
DbSeek(xFilial()+cCliente)
While !Eof() .and. cCliente == SCB->CB_CLIENTE
	If SCB->CB_LIBMAN == "S"
		If lMsg
			MsgInfo("A liberação deverá ser manual","CB_LIBMAN")
		EndIf
		lRet := .T.
		Exit
	EndIf
	DbSkip()
End
if !Empty(cSvAlias)
	DbSelectArea(cSvAlias)
Endif
Return lRet

User Function CLVincHL(cCodCli)
Local oDlg		:= Nil
Local oGetd		:= Nil
Local nIdxOrd	:= 0
Local nRecno	:= 0
Local ni		:= 0
Local nX		:= 0
Local xVar		:= ""
Local lUpdate 	:= .F.
Local cSvAlias 	:= Alias()
Local aBkpRotina := If(Type("aRotina") == "A",Aclone(aRotina),NIL)
Local _aRet		:= {}
Local _cNomeCli	:= ""
Local lVinc		:= .F.
Local oTISX 	:= TIGetSXS():New()
Local aFields 	:= {'A1_COD','A1_NOME'}

Private aHeader := {}
Private aCols 	:= {}
Private aRotina := {{"","",0,3}}

If cCodCli == NIL
	If ConPad1(,,,"SA1")
		cCodCli := SA1->A1_COD
	Else
		Return
	EndIf
EndIf

_cNomeCli	:= Alltrim(SA1->A1_NREDUZ)
_cLoja		:= Alltrim(SA1->A1_LOJA)

//Verifica se cliente a vicular já é vinculado a outro cliente
DbSelectArea("AI0")
AI0->(DbSetOrder(1))
If AI0->(DbSeek(xFilial("AI0")+cCodCli))
	If !Empty(AI0->AI0_XVINCH) .And. AllTrim(AI0->AI0_XVINCH) <> AllTrim(cCodCli)
		If MsgYesNo("Cliente Principal: "+AI0->AI0_XVINCH+" - "+Alltrim(Posicione("SA1",1,xFilial("SA1")+Alltrim(AI0->AI0_XVINCH),"A1_NOME"))+ENTER+;
					"Cliente Vinculado: "+cCodCli+" - "+Alltrim(Posicione("SA1",1,xFilial("SA1")+cCodCli,"A1_NOME"))+ENTER+;
					"Deseja excluir este vínculo?","Vínculo Encontrado")

			MsgRun(	AI0->AI0_XVINCH + "/" + AI0->AI0_LOJA + "-" + Alltrim(Posicione("SA1",1,xFilial("SA1")+Alltrim(AI0->AI0_XVINCH),"A1_NREDUZ")) + "...",;
					"Atualizando resumo de contrato",;
					{|| _aRet := U_PF4Resumo(Alltrim(AI0->AI0_XVINCH),AI0->AI0_LOJA,,cCodCli) })

			AI0->(RecLock("AI0",.F.))
				AI0->AI0_XVINCH := ""
			AI0->(MsUnlock())

			ApMsgInfo("Cliente desvinculado com sucesso!")
		Else
			lVinc := .T.
		EndIf
	EndIf
EndIf

If !lVinc

	dbSelectArea("SA1")
	SA1->(dbSetOrder(1))
	If MsSeek(xFilial("SA1")+cCodCli)

		oTISX:SetSX3Fields(aFields)

		For nX:= 1 to len(aFields)

			cFieldSA1 := Iif(oTISX:GetSX3FldValue(aFields[nX],'X3_CAMPO') == "A1_COD", "CODCLI", "NOMECLI")

			Aadd(aHeader,{	;
				X31X3Titulo("SX3"),;
				cFieldSA1,;
				oTISX:GetSX3FldValue(aFields[nX],'X3_PICTURE'),;
				oTISX:GetSX3FldValue(aFields[nX],'X3_TAMANHO'),;
				oTISX:GetSX3FldValue(aFields[nX],'X3_DECIMAL'),;
				Iif(cFieldSA1=="CODCLI","NaoVazio() .and. U_CLValVinc()",""),;
				oTISX:GetSX3FldValue(aFields[nX],'X3_USADO'),;
				oTISX:GetSX3FldValue(aFields[nX],'X3_TIPO'),;
				"SA1",;
				oTISX:GetSX3FldValue(aFields[nX],'X3_CONTEXT');
			})

		Next	

		DbSelectArea("SA1")
		nIdxOrd := IndexOrd()
		nRecno := Recno()
		
		AI0->(dbOrderNickName("AI0_XVINCH"))
		If AI0->(DbSeek(xFilial("AI0")+cCodCli))
			While AI0->(!Eof() .and. Trim(AI0->AI0_XVINCH) == Trim(cCodCli))
				SA1->(DbSeek(xFilial("SA1")+AI0->AI0_CODCLI+AI0->AI0_LOJA  ))
				Aadd(aCols,{AI0->AI0_CODCLI,SA1->A1_NOME,.F.})
				AI0->(DbSkip())
			End
		Else
			Aadd(aCols,{})
			For ni := 1 To Len(aHeader)
				If aHeader[ni][8] == "C"
					xVar := Space(aHeader[ni][4]) 
				ElseIf aHeader[ni][8] == "N"
					xVar := 0
				ElseIf aHeader[ni][8] == "D"
					xVar := Ctod("")
				EndIf
				Aadd(aCols[1],xVar)
			Next
			Aadd(aCols[1],.F.)
		Endif

		DbSelectArea("SA1")	
		SA1->(DbSetOrder(1))
		SA1->(DbSeek(xFilial("SA1") + cCodCli))
		
		DEFINE MSDIALOG oDlg TITLE "Cliente: " + cCodCli + " - " + _cNomeCli FROM 0,0 TO 400,700 PIXEL
		
		oGetd := MsGetDados():New(35,02,__DlgHeight(oDlg),__DlgWidth(oDlg),1,,,,.T.,{"CODCLI"},,,9999,,,,,oDlg,.F.)
		oGetd:lF3Header := .T.
		
		ACTIVATE MSDIALOG oDlg CENTER ON INIT EnchoiceBar(oDlg,{|| lUpdate := .T.,oDlg:End()},{|| oDlg:End()})
		
		DbSelectArea("SA1")
		DbSetOrder(1)
		AI0->(DbSetOrder(1))
		If lUpdate
			For ni := 1 To Len(aCols)
				If aCols[ni][1] <> cCodCli
					AI0->(DbSeek(xFilial() + aCols[ni][1]))
					RecLock("AI0",.F.)
					If aCols[ni][Len(aCols[ni])]
						AI0->AI0_XVINCH := ""
					Else
						AI0->AI0_XVINCH := cCodCli
					EndIf
					MsUnlock()
				EndIf
			Next
			
			MsgRun(	cCodCli + "/" + _cLoja + "-" + _cNomeCli + "...",;
				"Atualizando resumo de contrato",;
				{|| _aRet := U_PF4Resumo(cCodCli,_cLoja) })
		
			//Apresenta mensagem se conseguiui ou não atualizar cliente no HlCLoud
			If Valtype(_aRet[1]) == "L"
				If _aRet[1]
					ApMsgInfo("Contrato do cliente "+cCodCli+"/"+_cLoja+"-"+_cNomeCli+" atualizado com sucesso!")
				Else
					ApMsgInfo(_aRet[2])
				EndIf
			EndIf
			
		EndIf
		
		SA1->(DbSetOrder(nIdxOrd))
		SA1->(DbGoTo(nRecno))
		
		//Restaura area para evitar erro no browse ZDP
		if !Empty(cSvAlias) 
			DbSelectArea(cSvAlias)
		Endif
		
	Else
		MsgStop("O cliente "+AllTrim(SA1->A1_NOME)+" esta vinculado ao cliente: "+SA1->A1_VINCHL )
	EndIf

EndIf

If aBkpRotina <> NIL
	aRotina := Aclone(aBkpRotina)
EndIf

Return

User Function CLValVinc()
Local lRet := .T.

AI0->(DbSetOrder(7))
If AI0->(dbSeek(xFilial("SA1")+M->CODCLI))
	If !Empty(AI0->AI0_XVINCH)
		lRet := .F.
		aCols[n][2] := ""
		Alert("Cliente já vinculado ao código: "+AI0->AI0_CODCLI)
	EndIf
EndIf

AI0->(DbSetOrder(1))
AI0->(DbGoTop())
If !AI0->(dbSeek(xFilial("SA1")+M->CODCLI))
	lRet := .F.
	aCols[n][2] := ""
	MsgStop("Cliente não encontrado")
EndIf

If lRet
	SA1->(DbSeek(xFilial()+M->CODCLI))
	aCols[n][2] := SA1->A1_NOME
EndIf

Return lRet

User Function CliGetDir(cFile)
Local nAt1
Local nAt2
Local cDir := ""

If (nAt1 := At("\",cFile)) > 0 .and. (nAt2 := Rat("\",cFile)) > 0
	cDir := Subs(cFile,nAt1,nAt2)
EndIf
Return cDir

User Function SITCLI()
Local nPos
Local cSITCLI := ""

If aSITCLI == NIL
	aSITCLI := StrTokArr(GetSx3Cache("A1_XSITCLI","X3_CBOX"),";")
EndIf

nPos := Ascan(aSITCLI,{|x| Subs(x,1,1) == SA1->A1_XSITCLI})
If nPos > 0
	cSITCLI := aSITCLI[nPos]
	cSITCLI := Subs(cSITCLI,At("=",cSITCLI) + 1)
Else
	cSITCLI :=  SA1->A1_XSITCLI
EndIf
Return cSITCLI

User Function File2Server(cFileClient)
Local cFileServer := ""
Local nHdl := -1

MakeDir("\AP5CLI2")

While nHdl < 0
	cFileServer := "\AP5CLI2\" + CriaTrab(,.F.) + ".DTC"
	If File(cFileServer)
		nHdl :=Ferase(cFileServer)
	Else
		nHdl := 0
	EndIf
End

If nHdl < 0 .or. !__CopyFile(cFileClient,cFileServer)
	cFileServer := ""
EndIf

Return cFileServer

User Function File2Client(cFileServer,cFileClient)
Local lRet

lRet := __CopyFile(cFileServer,cFileClient)
Ferase(cFileServer)
Return lRet

User Function CLISetEnv(cCliEmp,cCliFil)

RPCSetType(3)
RPCSetEnv(cCliEmp,cCliFil,,,,,,,.F.)
OpenSM0()
U_CLIOpenSX(cCliEmp)
Return

User Function CLIOpenSX(cEmpresa)
Local i
Local aSxs := U_CLISxs()
Local cAlias
Local cArquivo
Local cDriver
Local cIndice
Local j

For i := 1 To Len(aSxs)
	cAlias 	:= IIF("SX"$aSxs[i,1],aSxs[i,1],"SIX")
	cArquivo:= aSxs[i,1] + cEmpresa + '0'
	
	// Primeiro procurar o Sindex por empresa.
	// Depois procura o Sindex compartilhado, se nao existir cria por empresa
	If ( cAlias == 'SIX' ) .And. ( ! File(cArquivo+GetDBExtension()) )
		If ( File( 'SINDEX' + GetDbExtension() ) )
			cArquivo := 'SINDEX'
		EndIf
	EndIf
     
	If ( ! File(cArquivo+GetDBExtension()) )
		Final( 'File ' + cArquivo + ' does not exist.' )
	EndIf

	#IFDEF WAXS
		cDriver := "DBFCDXAX"
	#ENDIF

	#IFDEF WCODB
		cDriver := "DBFCDXTTS"
	#ENDIF

	#IFDEF WNTX
		cDriver := "DBFNTX"
	#ENDIF

	IF Substr(aSxs[i,1],1,3) $ "SX1/SX6/SX8"
		#IFDEF XNTX
			cDriver := IIF(Substr(aSxs[i,1],1,3) $ "SX1/SX6/SX8","DBFNTX",cDriver)
		#ELSE
			cDriver := "DBFCDX"
		#ENDIF
	Endif

	MsOpenDbf( .T. ,cDriver,cArquivo,cAlias, .T., .F. ,.T.,.T.)
	 
	For j := 1 To Len(aSxs[i,2])
		cIndice := IIF("SX"$aSxs[i,1],cArquivo+Str(j,1),cArquivo)
		MsOpenIdx( cIndice,aSxs[i,2][j],.T.,.T.,,cArquivo)
	Next j
	 
	dbSetOrder(1)
Next 

DbCommitAll()
Return

Return

User Function CLISxs()
Local aSxs

aSxs :={{ "SIX", { "INDICE+ORDEM"		},"Dicionário de Índices"},;
		{ "SX1", { "X1_GRUPO+X1_ORDEM"	},"Dicionário de Perguntas"},;
		{ "SX2", { "X2_CHAVE"			},"Dicionário de Arquivos"},;
		{ "SX3", { "X3_ARQUIVO+X3_ORDEM","X3_CAMPO","X3_GRPSXG+X3_ARQUIVO+X3_ORDEM","X3_ARQUIVO+X3_FOLDER+X3_ORDEM"},"Dicionário de Dados"},;
		{ 'SX4', { 'X4_FILIAL+X4_USERID+X4_TIPFREQ+X4_FUNNAME', 'X4_USERID+X4_FILIAL+X4_TIPFREQ' }, 'Agenda de relatórios' }, ;
		{ "SX6", { 'X6_FIL+X6_VAR'		}, "Parametros do Sistema"},;
		{ "SX7", { "X7_CAMPO+X7_SEQUENC"},"Gatilhos do Sistema"},;
		{ "SXB", { "XB_ALIAS+XB_TIPO+XB_SEQ+XB_COLUNA"},"Consultas Padronizadas"}, ;
		{ "SXG", { "XG_GRUPO"			}, "Grupo de campos"},;
		{ "SXA", { "XA_ALIAS+XA_ORDEM"	}, "Título das pastas de cadastro" },; 
		{ 'SXD', { 'XD_FUNCAO'			} , 'Configuração de relatórios' } ,;    
		{ "SX9", { "X9_DOM","X9_CDOM+X9_DOM"} , "Dicionário de Relacionamentos"} }
Return aSxs

User Function CliCopyData()

If !U_CliPassword()
	Return
EndIf

	cFile := TablePath(aTables[ni])
Return


User Function CLUnZip()
Local oDlg

DEFINE MSDIALOG oDlg FROM 0,0 TO 100,100 PIXEL
ACTIVATE MSDIALOG oDlg CENTER ON INIT (UnZip(),oDlg:End())
Return

Static Function UnZip()
Local cClientDir
Local cZip

While !Empty(cZip := cGetFile("Arquivo MZP | *.mzp |","Abrir",,,.T.,/*GETF_ONLYSERVER*/))
	cClientDir := cGetFile("","Escolha o diretório",,,.T.,GETF_LOCALHARD+GETF_RETDIRECTORY)
	If !Empty(cClientDir)
		MsgRun("Descompactando "+cClientDir,,{|| MsDecomp(cZip,cClientDir)})
	EndIf
End
Return

User Function CLGARMail(cCodGAR,cCodCli)
Local cSvAlias := Alias()
Local cEMail := ""

If Empty(cCodGAR)
	DbSelectArea("SA1")
	DbSetOrder(1)
	DbSeek(xFilial()+cCodCli)
	cCodGAR := SA1->A1_RTEC
EndIf

DbSelectArea("SA9")
DbSetOrder(1)
If DbSeek(xFilial()+cCodGAR)
	cEMail := AllTrim(SA9->A9_EMAIL)
EndIf

If !Empty(cSvAlias)
	DbSelectArea(cSvAlias)
EndIf
Return cEMail

User Function CLVerName(cParam)
Local cRet := ""
Local nPos

If __aVersion == NIL
	__aVersion := {Array(8),Array(8)}
	__aVersion[1][1] := "3"; __aVersion[2][1] := "2.03"
	__aVersion[1][2] := "4"; __aVersion[2][2] := "2.04"
	__aVersion[1][3] := "5"; __aVersion[2][3] := "2.05/4.05"
	__aVersion[1][4] := "6"; __aVersion[2][4] := "2.06/4.06"
	__aVersion[1][5] := "7"; __aVersion[2][5] := "2.07/4.07/5.07"
	__aVersion[1][6] := "8"; __aVersion[2][6] := "5.08"
	__aVersion[1][7] := "9"; __aVersion[2][7] := "6.09"
	__aVersion[1][8] := "1"; __aVersion[2][8] := "7.10"
EndIf

nPos := Ascan(__aVersion[1],cParam)
If nPos > 0
	cRet := __aVersion[2][nPos]
EndIf
Return cRet
